/**
 * UITreeViewComponent.ts
 * 
 * 树形控件UI组件，用于显示层级数据结构
 */

import { UIComponent, UIComponentType, UIComponentProps } from './UIComponent';
import { Component } from '../../core/Component';
import { Vector2, Vector3 } from 'three';

/**
 * 树节点接口
 */
export interface TreeNode {
  /** 节点ID */
  id: string;
  /** 节点标签 */
  label: string;
  /** 节点图标 */
  icon?: string;
  /** 节点数据 */
  data?: any;
  /** 子节点 */
  children?: TreeNode[];
  /** 是否展开 */
  expanded?: boolean;
  /** 是否选中 */
  selected?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否可拖拽 */
  draggable?: boolean;
  /** 是否可放置 */
  droppable?: boolean;
  /** 节点类型 */
  type?: string;
  /** 自定义样式类 */
  className?: string;
  /** 工具提示 */
  tooltip?: string;
}

/**
 * 树形控件配置
 */
export interface TreeViewConfig {
  /** 是否显示连接线 */
  showLines: boolean;
  /** 是否显示图标 */
  showIcons: boolean;
  /** 是否支持多选 */
  multiSelect: boolean;
  /** 是否支持拖拽 */
  draggable: boolean;
  /** 是否支持复选框 */
  checkable: boolean;
  /** 是否支持搜索 */
  searchable: boolean;
  /** 节点高度 */
  nodeHeight: number;
  /** 缩进大小 */
  indentSize: number;
  /** 是否虚拟化 */
  virtualized: boolean;
  /** 展开动画持续时间 */
  expandDuration: number;
  /** 是否自动展开父节点 */
  autoExpandParent: boolean;
  /** 默认展开级别 */
  defaultExpandLevel: number;
}

/**
 * 树形控件事件
 */
export interface TreeViewEvents {
  onNodeSelect?: (node: TreeNode, selected: boolean) => void;
  onNodeExpand?: (node: TreeNode, expanded: boolean) => void;
  onNodeCheck?: (node: TreeNode, checked: boolean) => void;
  onNodeClick?: (node: TreeNode, event: MouseEvent) => void;
  onNodeDoubleClick?: (node: TreeNode, event: MouseEvent) => void;
  onNodeContextMenu?: (node: TreeNode, event: MouseEvent) => void;
  onNodeDragStart?: (node: TreeNode, event: DragEvent) => void;
  onNodeDragOver?: (node: TreeNode, event: DragEvent) => void;
  onNodeDrop?: (dragNode: TreeNode, dropNode: TreeNode, position: 'before' | 'after' | 'inside') => void;
  onSearch?: (keyword: string, matchedNodes: TreeNode[]) => void;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: TreeViewConfig = {
  showLines: true,
  showIcons: true,
  multiSelect: false,
  draggable: false,
  checkable: false,
  searchable: false,
  nodeHeight: 28,
  indentSize: 20,
  virtualized: false,
  expandDuration: 200,
  autoExpandParent: true,
  defaultExpandLevel: 1
};

/**
 * 树形控件组件
 */
export interface UITreeViewComponentProps extends UIComponentProps {
  config?: Partial<TreeViewConfig>;
  events?: TreeViewEvents;
  nodes?: TreeNode[];
}

export class UITreeViewComponent extends UIComponent {
  private nodes: TreeNode[] = [];
  private config: TreeViewConfig;
  private events: TreeViewEvents;
  private selectedNodes: Set<string> = new Set();
  private checkedNodes: Set<string> = new Set();
  private expandedNodes: Set<string> = new Set();
  private flattenedNodes: TreeNode[] = [];
  private filteredNodes: TreeNode[] = [];
  private searchKeyword: string = '';
  private containerElement?: HTMLElement;
  private visibleRange: { start: number; end: number } = { start: 0, end: 0 };

  constructor(props: UITreeViewComponentProps = {}) {
    super({ ...props, type: UIComponentType.TREE_VIEW });
    this.config = { ...DEFAULT_CONFIG, ...props.config };
    this.events = props.events || {};
    if (props.nodes) {
      this.nodes = props.nodes;
    }
  }

  /**
   * 初始化组件
   */
  public initialize(): void {
    this.createTreeElement();
    this.updateFlattenedNodes();
    this.render();
  }

  /**
   * 设置树节点数据
   */
  public setNodes(nodes: TreeNode[]): void {
    this.nodes = nodes;
    this.initializeNodeStates();
    this.updateFlattenedNodes();
    this.render();
  }

  /**
   * 获取树节点数据
   */
  public getNodes(): TreeNode[] {
    return this.nodes;
  }

  /**
   * 添加节点
   */
  public addNode(node: TreeNode, parentId?: string): void {
    if (parentId) {
      const parent = this.findNode(parentId);
      if (parent) {
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(node);
      }
    } else {
      this.nodes.push(node);
    }
    
    this.updateFlattenedNodes();
    this.render();
  }

  /**
   * 移除节点
   */
  public removeNode(nodeId: string): void {
    const removeFromArray = (nodes: TreeNode[]): boolean => {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === nodeId) {
          nodes.splice(i, 1);
          return true;
        }
        if (nodes[i].children && removeFromArray(nodes[i].children!)) {
          return true;
        }
      }
      return false;
    };

    removeFromArray(this.nodes);
    this.selectedNodes.delete(nodeId);
    this.checkedNodes.delete(nodeId);
    this.expandedNodes.delete(nodeId);
    
    this.updateFlattenedNodes();
    this.render();
  }

  /**
   * 更新节点
   */
  public updateNode(nodeId: string, updates: Partial<TreeNode>): void {
    const node = this.findNode(nodeId);
    if (node) {
      Object.assign(node, updates);
      this.updateFlattenedNodes();
      this.render();
    }
  }

  /**
   * 查找节点
   */
  public findNode(nodeId: string): TreeNode | null {
    const findInArray = (nodes: TreeNode[]): TreeNode | null => {
      for (const node of nodes) {
        if (node.id === nodeId) {
          return node;
        }
        if (node.children) {
          const found = findInArray(node.children);
          if (found) return found;
        }
      }
      return null;
    };

    return findInArray(this.nodes);
  }

  /**
   * 展开节点
   */
  public expandNode(nodeId: string, expanded: boolean = true): void {
    const node = this.findNode(nodeId);
    if (!node) return;

    if (expanded) {
      this.expandedNodes.add(nodeId);
      node.expanded = true;
      
      // 自动展开父节点
      if (this.config.autoExpandParent) {
        this.expandParentNodes(nodeId);
      }
    } else {
      this.expandedNodes.delete(nodeId);
      node.expanded = false;
    }

    this.events.onNodeExpand?.(node, expanded);
    this.updateFlattenedNodes();
    this.render();
  }

  /**
   * 展开所有节点
   */
  public expandAll(): void {
    const expandRecursive = (nodes: TreeNode[]) => {
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          this.expandedNodes.add(node.id);
          node.expanded = true;
          expandRecursive(node.children);
        }
      });
    };

    expandRecursive(this.nodes);
    this.updateFlattenedNodes();
    this.render();
  }

  /**
   * 折叠所有节点
   */
  public collapseAll(): void {
    this.expandedNodes.clear();
    
    const collapseRecursive = (nodes: TreeNode[]) => {
      nodes.forEach(node => {
        node.expanded = false;
        if (node.children) {
          collapseRecursive(node.children);
        }
      });
    };

    collapseRecursive(this.nodes);
    this.updateFlattenedNodes();
    this.render();
  }

  /**
   * 选择节点
   */
  public selectNode(nodeId: string, selected: boolean = true): void {
    const node = this.findNode(nodeId);
    if (!node || node.disabled) return;

    if (selected) {
      if (!this.config.multiSelect) {
        this.selectedNodes.clear();
        this.clearNodeSelection(this.nodes);
      }
      this.selectedNodes.add(nodeId);
      node.selected = true;
    } else {
      this.selectedNodes.delete(nodeId);
      node.selected = false;
    }

    this.events.onNodeSelect?.(node, selected);
    this.render();
  }

  /**
   * 获取选中的节点
   */
  public getSelectedNodes(): TreeNode[] {
    return Array.from(this.selectedNodes).map(id => this.findNode(id)!).filter(Boolean);
  }

  /**
   * 搜索节点
   */
  public search(keyword: string): void {
    this.searchKeyword = keyword.toLowerCase();
    
    if (!keyword) {
      this.filteredNodes = [];
      this.updateFlattenedNodes();
      this.render();
      return;
    }

    const matchedNodes: TreeNode[] = [];
    const searchRecursive = (nodes: TreeNode[]): TreeNode[] => {
      const filtered: TreeNode[] = [];
      
      nodes.forEach(node => {
        const matches = node.label.toLowerCase().includes(this.searchKeyword);
        let childMatches: TreeNode[] = [];
        
        if (node.children) {
          childMatches = searchRecursive(node.children);
        }
        
        if (matches || childMatches.length > 0) {
          const filteredNode: TreeNode = {
            ...node,
            children: childMatches.length > 0 ? childMatches : undefined,
            expanded: childMatches.length > 0 ? true : node.expanded
          };
          
          filtered.push(filteredNode);
          
          if (matches) {
            matchedNodes.push(node);
          }
        }
      });
      
      return filtered;
    };

    this.filteredNodes = searchRecursive(this.nodes);
    this.events.onSearch?.(keyword, matchedNodes);
    this.updateFlattenedNodes();
    this.render();
  }

  /**
   * 清除搜索
   */
  public clearSearch(): void {
    this.search('');
  }

  /**
   * 滚动到节点
   */
  public scrollToNode(nodeId: string): void {
    const index = this.flattenedNodes.findIndex(node => node.id === nodeId);
    if (index !== -1 && this.containerElement) {
      const scrollTop = index * this.config.nodeHeight;
      this.containerElement.scrollTop = scrollTop;
    }
  }

  /**
   * 初始化节点状态
   */
  private initializeNodeStates(): void {
    const initRecursive = (nodes: TreeNode[], level: number = 0) => {
      nodes.forEach(node => {
        // 设置默认展开状态
        if (level < this.config.defaultExpandLevel && node.children && node.children.length > 0) {
          node.expanded = true;
          this.expandedNodes.add(node.id);
        }
        
        // 初始化选中状态
        if (node.selected) {
          this.selectedNodes.add(node.id);
        }
        
        // 递归处理子节点
        if (node.children) {
          initRecursive(node.children, level + 1);
        }
      });
    };

    initRecursive(this.nodes);
  }

  /**
   * 更新扁平化节点列表
   */
  private updateFlattenedNodes(): void {
    this.flattenedNodes = [];
    const nodesToProcess = this.searchKeyword ? this.filteredNodes : this.nodes;
    
    const flattenRecursive = (nodes: TreeNode[], level: number = 0) => {
      nodes.forEach(node => {
        this.flattenedNodes.push({ ...node, level } as TreeNode & { level: number });
        
        if (node.expanded && node.children) {
          flattenRecursive(node.children, level + 1);
        }
      });
    };

    flattenRecursive(nodesToProcess);
    
    // 更新可见范围
    if (this.config.virtualized && this.containerElement) {
      this.updateVisibleRange();
    }
  }

  /**
   * 更新可见范围（虚拟化）
   */
  private updateVisibleRange(): void {
    if (!this.containerElement) return;

    const containerHeight = this.containerElement.clientHeight;
    const scrollTop = this.containerElement.scrollTop;
    const nodeHeight = this.config.nodeHeight;
    
    const startIndex = Math.floor(scrollTop / nodeHeight);
    const endIndex = Math.min(
      this.flattenedNodes.length - 1,
      Math.ceil((scrollTop + containerHeight) / nodeHeight)
    );
    
    this.visibleRange = { start: startIndex, end: endIndex };
  }

  /**
   * 展开父节点
   */
  private expandParentNodes(nodeId: string): void {
    const findParent = (nodes: TreeNode[], targetId: string, parent?: TreeNode): TreeNode | null => {
      for (const node of nodes) {
        if (node.id === targetId) {
          return parent || null;
        }
        if (node.children) {
          const found = findParent(node.children, targetId, node);
          if (found) return found;
        }
      }
      return null;
    };

    let parent = findParent(this.nodes, nodeId);
    while (parent) {
      this.expandedNodes.add(parent.id);
      parent.expanded = true;
      parent = findParent(this.nodes, parent.id);
    }
  }

  /**
   * 清除节点选中状态
   */
  private clearNodeSelection(nodes: TreeNode[]): void {
    nodes.forEach(node => {
      node.selected = false;
      if (node.children) {
        this.clearNodeSelection(node.children);
      }
    });
  }

  /**
   * 创建树形控件元素
   */
  private createTreeElement(): void {
    if (!this.htmlElement) return;

    this.containerElement = document.createElement('div');
    this.containerElement.className = 'ui-tree-view';
    this.containerElement.style.cssText = `
      width: 100%;
      height: 100%;
      overflow: auto;
      font-family: var(--ui-font-primary);
      font-size: var(--ui-font-size-md);
      color: var(--ui-color-text);
      background: var(--ui-color-background);
      border: 1px solid var(--ui-color-border);
    `;

    if (this.config.virtualized) {
      this.containerElement.addEventListener('scroll', () => {
        this.updateVisibleRange();
        this.render();
      });
    }

    this.htmlElement.appendChild(this.containerElement);
  }

  /**
   * 渲染树形控件
   */
  public render(): void {
    if (!this.containerElement) return;

    const nodesToRender = this.config.virtualized 
      ? this.flattenedNodes.slice(this.visibleRange.start, this.visibleRange.end + 1)
      : this.flattenedNodes;

    this.containerElement.innerHTML = '';

    if (this.config.virtualized) {
      // 创建虚拟滚动容器
      const spacer = document.createElement('div');
      spacer.style.height = `${this.flattenedNodes.length * this.config.nodeHeight}px`;
      spacer.style.position = 'relative';
      
      const content = document.createElement('div');
      content.style.position = 'absolute';
      content.style.top = `${this.visibleRange.start * this.config.nodeHeight}px`;
      content.style.width = '100%';
      
      nodesToRender.forEach((node, index) => {
        const nodeElement = this.createNodeElement(node, this.visibleRange.start + index);
        content.appendChild(nodeElement);
      });
      
      spacer.appendChild(content);
      this.containerElement.appendChild(spacer);
    } else {
      nodesToRender.forEach((node, index) => {
        const nodeElement = this.createNodeElement(node, index);
        this.containerElement!.appendChild(nodeElement);
      });
    }
  }

  /**
   * 创建节点元素
   */
  private createNodeElement(node: TreeNode & { level?: number }, _index: number): HTMLElement {
    const nodeElement = document.createElement('div');
    const level = (node as any).level || 0;
    const hasChildren = node.children && node.children.length > 0;
    
    nodeElement.className = `tree-node ${node.className || ''}`;
    nodeElement.style.cssText = `
      height: ${this.config.nodeHeight}px;
      padding-left: ${level * this.config.indentSize + 8}px;
      display: flex;
      align-items: center;
      cursor: pointer;
      user-select: none;
      border-bottom: 1px solid var(--ui-color-border);
    `;

    if (node.selected) {
      nodeElement.style.backgroundColor = 'var(--ui-color-primary)';
      nodeElement.style.color = 'white';
    }

    if (node.disabled) {
      nodeElement.style.opacity = '0.5';
      nodeElement.style.cursor = 'not-allowed';
    }

    // 展开/折叠图标
    if (hasChildren) {
      const expandIcon = document.createElement('span');
      expandIcon.className = 'expand-icon';
      expandIcon.textContent = node.expanded ? '▼' : '▶';
      expandIcon.style.cssText = `
        margin-right: 4px;
        font-size: 10px;
        transition: transform 0.2s ease;
      `;
      
      expandIcon.addEventListener('click', (e) => {
        e.stopPropagation();
        this.expandNode(node.id, !node.expanded);
      });
      
      nodeElement.appendChild(expandIcon);
    } else if (this.config.showLines) {
      const spacer = document.createElement('span');
      spacer.style.width = '14px';
      spacer.style.marginRight = '4px';
      nodeElement.appendChild(spacer);
    }

    // 节点图标
    if (this.config.showIcons && node.icon) {
      const icon = document.createElement('span');
      icon.className = 'node-icon';
      icon.textContent = node.icon;
      icon.style.marginRight = '6px';
      nodeElement.appendChild(icon);
    }

    // 节点标签
    const label = document.createElement('span');
    label.className = 'node-label';
    label.textContent = node.label;
    label.style.flex = '1';
    nodeElement.appendChild(label);

    // 事件处理
    nodeElement.addEventListener('click', (e) => {
      if (!node.disabled) {
        this.selectNode(node.id, !node.selected);
        this.events.onNodeClick?.(node, e);
      }
    });

    nodeElement.addEventListener('dblclick', (e) => {
      if (!node.disabled) {
        this.events.onNodeDoubleClick?.(node, e);
      }
    });

    nodeElement.addEventListener('contextmenu', (e) => {
      if (!node.disabled) {
        this.events.onNodeContextMenu?.(node, e);
      }
    });

    // 拖拽支持
    if (this.config.draggable && node.draggable !== false) {
      nodeElement.draggable = true;
      
      nodeElement.addEventListener('dragstart', (e) => {
        this.events.onNodeDragStart?.(node, e);
      });
      
      nodeElement.addEventListener('dragover', (e) => {
        e.preventDefault();
        this.events.onNodeDragOver?.(node, e);
      });
    }

    return nodeElement;
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new UITreeViewComponent({
      id: this.id,
      type: this.uiType,
      visible: this.visible,
      interactive: this.interactive,
      position: this.position instanceof Vector3 ? this.position.clone() : new Vector2(this.position.x, this.position.y),
      size: new Vector2(this.size.x, this.size.y),
      opacity: this.opacity,
      zIndex: this.zIndex,
      layoutType: this.layoutType,
      layoutParams: { ...this.layoutParams },
      backgroundColor: this.backgroundColor,
      borderColor: this.borderColor,
      borderWidth: this.borderWidth,
      borderRadius: this.borderRadius,
      padding: { ...this.padding },
      margin: { ...this.margin },
      data: { ...this.data },
      tags: [...this.tags],
      is3D: this.is3D,
      config: { ...this.config },
      nodes: [...this.nodes],
      events: { ...this.events }
    } as UITreeViewComponentProps);
  }
}
