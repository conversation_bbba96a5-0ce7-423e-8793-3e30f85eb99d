/**
 * UIVirtualScrollComponent.ts
 * 
 * 虚拟滚动UI组件，用于高效渲染大量列表项
 */

import { UIComponent, UIComponentType, UIComponentProps } from './UIComponent';
import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';
import { Vector2, Vector3 } from 'three';

/**
 * 虚拟滚动项目接口
 */
export interface VirtualScrollItem {
  /** 项目ID */
  id: string;
  /** 项目数据 */
  data: any;
  /** 项目高度（可选，用于动态高度） */
  height?: number;
}

/**
 * 虚拟滚动配置
 */
export interface VirtualScrollConfig {
  /** 项目高度（固定高度模式） */
  itemHeight: number;
  /** 容器高度 */
  containerHeight: number;
  /** 缓冲区项目数 */
  bufferSize: number;
  /** 是否启用动态高度 */
  dynamicHeight: boolean;
  /** 滚动阈值（像素） */
  scrollThreshold: number;
}

export interface UIVirtualScrollComponentProps extends UIComponentProps {
  config?: Partial<VirtualScrollConfig>;
  items?: VirtualScrollItem[];
  onItemRender?: (item: VirtualScrollItem, element: Entity) => void;
  onScroll?: (scrollTop: number, scrollLeft: number) => void;
}

/**
 * 虚拟滚动组件
 */
export class UIVirtualScrollComponent extends UIComponent {
  private items: VirtualScrollItem[] = [];
  private config: VirtualScrollConfig;
  private scrollTop: number = 0;
  private visibleStartIndex: number = 0;
  private visibleEndIndex: number = 0;
  private renderedItems: Map<string, Entity> = new Map();
  private itemHeights: Map<string, number> = new Map();
  private totalHeight: number = 0;
  private scrollContainer?: HTMLElement;
  private onItemRender?: (item: VirtualScrollItem, element: Entity) => void;
  private onScroll?: (scrollTop: number, scrollLeft: number) => void;

  constructor(props: UIVirtualScrollComponentProps = {}) {
    super({ ...props, type: UIComponentType.VIRTUAL_SCROLL });

    this.config = {
      itemHeight: 40,
      containerHeight: 400,
      bufferSize: 5,
      dynamicHeight: false,
      scrollThreshold: 10,
      ...props.config
    };

    if (props.items) {
      this.items = props.items;
    }

    this.onItemRender = props.onItemRender;
    this.onScroll = props.onScroll;
  }

  /**
   * 初始化组件
   */
  public initialize(): void {
    this.setupScrollContainer();
    this.calculateVisibleRange();
  }

  /**
   * 更新组件
   */
  public update(deltaTime: number): void {
    super.update(deltaTime);
    this.updateVisibleItems();
  }

  /**
   * 设置配置
   */
  public setConfig(config: Partial<VirtualScrollConfig>): void {
    this.config = { ...this.config, ...config };
    this.calculateTotalHeight();
    this.calculateVisibleRange();
  }

  /**
   * 设置项目列表
   */
  public setItems(items: VirtualScrollItem[]): void {
    this.items = items;
    this.calculateTotalHeight();
    this.calculateVisibleRange();
    this.renderVisibleItems();
  }

  /**
   * 添加项目
   */
  public addItem(item: VirtualScrollItem, index?: number): void {
    if (index !== undefined) {
      this.items.splice(index, 0, item);
    } else {
      this.items.push(item);
    }
    
    this.calculateTotalHeight();
    this.calculateVisibleRange();
    this.renderVisibleItems();
  }

  /**
   * 移除项目
   */
  public removeItem(id: string): void {
    const index = this.items.findIndex(item => item.id === id);
    if (index !== -1) {
      this.items.splice(index, 1);
      this.itemHeights.delete(id);
      
      // 移除已渲染的项目
      const renderedItem = this.renderedItems.get(id);
      if (renderedItem) {
        this.destroyItemElement(renderedItem);
        this.renderedItems.delete(id);
      }
      
      this.calculateTotalHeight();
      this.calculateVisibleRange();
      this.renderVisibleItems();
    }
  }

  /**
   * 更新项目
   */
  public updateItem(id: string, data: any): void {
    const item = this.items.find(item => item.id === id);
    if (item) {
      item.data = data;
      
      // 如果项目已渲染，更新其内容
      const renderedItem = this.renderedItems.get(id);
      if (renderedItem && this.onItemRender) {
        this.onItemRender(item, renderedItem);
      }
    }
  }

  /**
   * 滚动到指定项目
   */
  public scrollToItem(id: string, alignment: 'start' | 'center' | 'end' = 'start'): void {
    const index = this.items.findIndex(item => item.id === id);
    if (index === -1) return;

    const itemTop = this.getItemTop(index);
    let scrollTop = itemTop;

    switch (alignment) {
      case 'center':
        scrollTop = itemTop - this.config.containerHeight / 2 + this.getItemHeight(index) / 2;
        break;
      case 'end':
        scrollTop = itemTop - this.config.containerHeight + this.getItemHeight(index);
        break;
    }

    scrollTop = Math.max(0, Math.min(scrollTop, this.totalHeight - this.config.containerHeight));
    this.setScrollTop(scrollTop);
  }

  /**
   * 滚动到指定索引
   */
  public scrollToIndex(index: number, alignment: 'start' | 'center' | 'end' = 'start'): void {
    if (index < 0 || index >= this.items.length) return;
    
    const item = this.items[index];
    this.scrollToItem(item.id, alignment);
  }

  /**
   * 设置滚动位置
   */
  public setScrollTop(scrollTop: number): void {
    this.scrollTop = Math.max(0, Math.min(scrollTop, this.totalHeight - this.config.containerHeight));
    
    if (this.scrollContainer) {
      this.scrollContainer.scrollTop = this.scrollTop;
    }
    
    this.calculateVisibleRange();
    this.renderVisibleItems();
    
    if (this.onScroll) {
      this.onScroll(this.scrollTop, 0);
    }
  }

  /**
   * 获取滚动位置
   */
  public getScrollTop(): number {
    return this.scrollTop;
  }

  /**
   * 设置项目渲染回调
   */
  public setItemRenderer(callback: (item: VirtualScrollItem, element: Entity) => void): void {
    this.onItemRender = callback;
  }

  /**
   * 设置滚动回调
   */
  public setScrollCallback(callback: (scrollTop: number, scrollLeft: number) => void): void {
    this.onScroll = callback;
  }

  /**
   * 获取可见项目范围
   */
  public getVisibleRange(): { start: number; end: number } {
    return {
      start: this.visibleStartIndex,
      end: this.visibleEndIndex
    };
  }

  /**
   * 获取总高度
   */
  public getTotalHeight(): number {
    return this.totalHeight;
  }

  /**
   * 设置项目高度（动态高度模式）
   */
  public setItemHeight(id: string, height: number): void {
    if (this.config.dynamicHeight) {
      this.itemHeights.set(id, height);
      this.calculateTotalHeight();
      this.calculateVisibleRange();
    }
  }

  /**
   * 设置滚动容器
   */
  private setupScrollContainer(): void {
    // 这里应该创建或获取滚动容器元素
    // 具体实现取决于UI系统的DOM集成
  }

  /**
   * 计算总高度
   */
  private calculateTotalHeight(): void {
    if (this.config.dynamicHeight) {
      this.totalHeight = 0;
      for (let i = 0; i < this.items.length; i++) {
        this.totalHeight += this.getItemHeight(i);
      }
    } else {
      this.totalHeight = this.items.length * this.config.itemHeight;
    }
  }

  /**
   * 计算可见范围
   */
  private calculateVisibleRange(): void {
    if (this.items.length === 0) {
      this.visibleStartIndex = 0;
      this.visibleEndIndex = 0;
      return;
    }

    const containerHeight = this.config.containerHeight;
    const bufferSize = this.config.bufferSize;

    if (this.config.dynamicHeight) {
      // 动态高度模式
      this.visibleStartIndex = this.findStartIndex(this.scrollTop);
      this.visibleEndIndex = this.findEndIndex(this.scrollTop + containerHeight);
    } else {
      // 固定高度模式
      this.visibleStartIndex = Math.floor(this.scrollTop / this.config.itemHeight);
      this.visibleEndIndex = Math.ceil((this.scrollTop + containerHeight) / this.config.itemHeight);
    }

    // 添加缓冲区
    this.visibleStartIndex = Math.max(0, this.visibleStartIndex - bufferSize);
    this.visibleEndIndex = Math.min(this.items.length - 1, this.visibleEndIndex + bufferSize);
  }

  /**
   * 查找开始索引（动态高度）
   */
  private findStartIndex(scrollTop: number): number {
    let currentTop = 0;
    for (let i = 0; i < this.items.length; i++) {
      const itemHeight = this.getItemHeight(i);
      if (currentTop + itemHeight > scrollTop) {
        return i;
      }
      currentTop += itemHeight;
    }
    return this.items.length - 1;
  }

  /**
   * 查找结束索引（动态高度）
   */
  private findEndIndex(scrollBottom: number): number {
    let currentTop = 0;
    for (let i = 0; i < this.items.length; i++) {
      const itemHeight = this.getItemHeight(i);
      currentTop += itemHeight;
      if (currentTop >= scrollBottom) {
        return i;
      }
    }
    return this.items.length - 1;
  }

  /**
   * 获取项目高度
   */
  private getItemHeight(index: number): number {
    if (this.config.dynamicHeight) {
      const item = this.items[index];
      return this.itemHeights.get(item.id) || item.height || this.config.itemHeight;
    }
    return this.config.itemHeight;
  }

  /**
   * 获取项目顶部位置
   */
  private getItemTop(index: number): number {
    if (this.config.dynamicHeight) {
      let top = 0;
      for (let i = 0; i < index; i++) {
        top += this.getItemHeight(i);
      }
      return top;
    }
    return index * this.config.itemHeight;
  }

  /**
   * 更新可见项目
   */
  private updateVisibleItems(): void {
    // 检查是否需要重新计算可见范围
    if (this.scrollContainer) {
      const newScrollTop = this.scrollContainer.scrollTop;
      if (Math.abs(newScrollTop - this.scrollTop) > this.config.scrollThreshold) {
        this.scrollTop = newScrollTop;
        this.calculateVisibleRange();
        this.renderVisibleItems();
        
        if (this.onScroll) {
          this.onScroll(this.scrollTop, this.scrollContainer.scrollLeft || 0);
        }
      }
    }
  }

  /**
   * 渲染可见项目
   */
  private renderVisibleItems(): void {
    // 移除不再可见的项目
    for (const [id, element] of this.renderedItems) {
      const index = this.items.findIndex(item => item.id === id);
      if (index < this.visibleStartIndex || index > this.visibleEndIndex) {
        this.destroyItemElement(element);
        this.renderedItems.delete(id);
      }
    }

    // 渲染新的可见项目
    for (let i = this.visibleStartIndex; i <= this.visibleEndIndex; i++) {
      const item = this.items[i];
      if (!this.renderedItems.has(item.id)) {
        const element = this.createItemElement(item, i);
        this.renderedItems.set(item.id, element);
      }
    }
  }

  /**
   * 创建项目元素
   */
  private createItemElement(item: VirtualScrollItem, _index: number): Entity {
    const element = new Entity();

    // 设置项目位置
    // const top = this.getItemTop(index);
    // 这里应该设置元素的位置和尺寸

    // 调用渲染回调
    if (this.onItemRender) {
      this.onItemRender(item, element);
    }

    return element;
  }

  /**
   * 销毁项目元素
   */
  private destroyItemElement(_element: Entity): void {
    // 这里应该清理元素资源
    // element.destroy(); // Entity类可能没有destroy方法，需要根据实际API调整
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new UIVirtualScrollComponent({
      id: this.id,
      type: this.uiType,
      visible: this.visible,
      interactive: this.interactive,
      position: this.position instanceof Vector3 ? this.position.clone() : new Vector2(this.position.x, this.position.y),
      size: new Vector2(this.size.x, this.size.y),
      opacity: this.opacity,
      zIndex: this.zIndex,
      layoutType: this.layoutType,
      layoutParams: { ...this.layoutParams },
      backgroundColor: this.backgroundColor,
      borderColor: this.borderColor,
      borderWidth: this.borderWidth,
      borderRadius: this.borderRadius,
      padding: { ...this.padding },
      margin: { ...this.margin },
      data: { ...this.data },
      tags: [...this.tags],
      is3D: this.is3D,
      config: { ...this.config },
      items: [...this.items],
      onItemRender: this.onItemRender,
      onScroll: this.onScroll
    } as UIVirtualScrollComponentProps);
  }
}
