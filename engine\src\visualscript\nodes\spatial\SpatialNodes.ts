/**
 * 空间信息系统视觉脚本节点
 * 为视觉脚本系统提供空间信息相关的节点
 */
import { FlowNode, FlowNodeOptions } from '../FlowNode';
import { NodeCategory, SocketDirection, SocketType } from '../Node';
import { ExecutionContext } from '../../execution/ExecutionContext';
import {
  GeographicCoordinate,
  CoordinateSystemType,
  CoordinateSystemManager
} from '../../../spatial/coordinate/CoordinateSystem';
import { GeospatialComponent } from '../../../spatial/components/GeospatialComponent';
import { SpatialAnalysisEngine } from '../../../spatial/analysis/SpatialAnalysisEngine';

/**
 * 创建地理坐标节点
 */
export class CreateGeographicCoordinateNode extends FlowNode {
  public readonly category: NodeCategory = NodeCategory.SPATIAL;

  constructor(options: FlowNodeOptions) {
    super(options);

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '创建地理坐标';
    }
    if (!this.metadata.description) {
      this.metadata.description = '创建地理坐标对象';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'longitude',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '经度',
      defaultValue: 0
    });

    this.addInput({
      name: 'latitude',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '纬度',
      defaultValue: 0
    });

    this.addInput({
      name: 'altitude',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '海拔',
      defaultValue: 0
    });

    this.addOutput({
      name: 'coordinate',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '坐标'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    const longitude = inputs.longitude as number;
    const latitude = inputs.latitude as number;
    const altitude = inputs.altitude as number;

    const coordinate: GeographicCoordinate = {
      longitude,
      latitude,
      altitude: altitude || undefined
    };

    this.setOutputValue('coordinate', coordinate);
    return this.outputFlowNames[0];
  }
}

/**
 * 坐标转换节点
 */
export class CoordinateTransformNode extends FlowNode {
  public readonly category: NodeCategory = NodeCategory.SPATIAL;

  constructor(options: FlowNodeOptions) {
    super(options);

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '坐标转换';
    }
    if (!this.metadata.description) {
      this.metadata.description = '转换地理坐标系统';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'coordinate',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '输入坐标'
    });

    this.addInput({
      name: 'sourceSystem',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '源坐标系',
      defaultValue: 'WGS84'
    });

    this.addInput({
      name: 'targetSystem',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标坐标系',
      defaultValue: 'GCJ02'
    });

    this.addOutput({
      name: 'transformedCoordinate',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '转换后坐标'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '转换成功'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    const coordinate = inputs.coordinate as GeographicCoordinate;
    const sourceSystem = inputs.sourceSystem as string;
    const targetSystem = inputs.targetSystem as string;

    try {
      const manager = CoordinateSystemManager.getInstance();
      const result = manager.transform(
        coordinate,
        sourceSystem as CoordinateSystemType,
        targetSystem as CoordinateSystemType
      );

      this.setOutputValue('transformedCoordinate', result);
      this.setOutputValue('success', true);
    } catch (error) {
      console.error('坐标转换失败:', error);
      this.setOutputValue('transformedCoordinate', coordinate);
      this.setOutputValue('success', false);
    }

    return this.outputFlowNames[0];
  }
}

/**
 * 创建地理空间组件节点
 */
export class CreateGeospatialComponentNode extends FlowNode {
  public readonly category: NodeCategory = NodeCategory.SPATIAL;

  constructor(options: FlowNodeOptions) {
    super(options);

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '创建地理空间组件';
    }
    if (!this.metadata.description) {
      this.metadata.description = '创建地理空间组件对象';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'coordinate',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '地理坐标'
    });

    this.addInput({
      name: 'geometryType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '几何类型',
      defaultValue: 'Point'
    });

    this.addInput({
      name: 'coordinateSystem',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '坐标系',
      defaultValue: 'WGS84'
    });

    this.addInput({
      name: 'properties',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '属性',
      defaultValue: {}
    });

    this.addOutput({
      name: 'component',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '地理空间组件'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    const coordinate = inputs.coordinate as GeographicCoordinate;
    const geometryType = inputs.geometryType as 'Point' | 'LineString' | 'Polygon';
    const coordinateSystem = inputs.coordinateSystem as CoordinateSystemType;
    const properties = inputs.properties as any;

    const component = new GeospatialComponent(coordinate, coordinateSystem, geometryType);
    component.setProperties(properties);

    this.setOutputValue('component', component);
    return this.outputFlowNames[0];
  }
}

/**
 * 计算距离节点
 */
export class CalculateDistanceNode extends FlowNode {
  public readonly category: NodeCategory = NodeCategory.SPATIAL;

  constructor(options: FlowNodeOptions) {
    super(options);

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '计算距离';
    }
    if (!this.metadata.description) {
      this.metadata.description = '计算两个地理坐标之间的距离';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'coordinate1',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '坐标1'
    });

    this.addInput({
      name: 'coordinate2',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '坐标2'
    });

    this.addOutput({
      name: 'distance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '距离(米)'
    });

    this.addOutput({
      name: 'distanceKm',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '距离(公里)'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    const coord1 = inputs.coordinate1 as GeographicCoordinate;
    const coord2 = inputs.coordinate2 as GeographicCoordinate;

    if (coord1 && coord2) {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const distance = analysisEngine.haversineDistance(coord1, coord2);

      this.setOutputValue('distance', distance);
      this.setOutputValue('distanceKm', distance / 1000);
    } else {
      this.setOutputValue('distance', 0);
      this.setOutputValue('distanceKm', 0);
    }

    return this.outputFlowNames[0];
  }
}


